-- =====================================================
-- CONSTRAINT FIX: Update customer_debts_reasonable_price
-- =====================================================
-- This script fixes the constraint violation issue by increasing
-- the maximum allowed product price from ₱10,000 to ₱100,000
-- to accommodate SUKLI_ADJUSTMENT records and large transactions.

-- Drop the existing constraint
ALTER TABLE customer_debts 
DROP CONSTRAINT IF EXISTS customer_debts_reasonable_price;

-- Add the updated constraint with higher limit
ALTER TABLE customer_debts 
ADD CONSTRAINT customer_debts_reasonable_price 
CHECK (product_price <= 100000.00);

-- Verify the constraint was updated successfully
SELECT 
    constraint_name,
    check_clause
FROM information_schema.check_constraints 
WHERE constraint_name = 'customer_debts_reasonable_price';

-- Test the constraint with a sample insert (will be rolled back)
BEGIN;
    INSERT INTO customer_debts (
        customer_name,
        customer_family_name,
        product_name,
        product_price,
        quantity,
        debt_date,
        notes
    ) VALUES (
        'Test',
        'Customer',
        'SUKLI_ADJUSTMENT',
        50000.00,
        1,
        CURRENT_DATE,
        'Test constraint fix - this will be rolled back'
    );
    
    -- If we reach here, the constraint allows the value
    SELECT 'Constraint fix successful - large amounts now allowed' as status;
ROLLBACK;

-- Display current constraints for verification
SELECT
    'Current Constraints' as info,
    constraint_name,
    constraint_type,
    check_clause
FROM information_schema.table_constraints tc
LEFT JOIN information_schema.check_constraints cc 
    ON tc.constraint_name = cc.constraint_name
WHERE tc.table_name = 'customer_debts'
    AND tc.constraint_type = 'CHECK'
ORDER BY tc.constraint_name;

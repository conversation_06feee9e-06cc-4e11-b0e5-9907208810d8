import fs from 'fs'
import path from 'path'

import { NextRequest } from 'next/server'

import {
  successResponse,
  errorResponse,
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  validateRequestBody,
  validateRequiredFields,
  handleDatabaseError,
  handleCorsPreflightRequest
} from '@/lib/api-utils'
import { logApi, logDb, logError } from '@/lib/logger'
import { supabase, withRetry } from '@/lib/supabase'
import type { DatabaseError, ProductsApiResponse, Product } from '@/types'

// Fallback mock data function
function getMockProducts() {
  try {
    const mockDataPath = path.join(process.cwd(), 'src', 'data', 'mockProducts.json')
    const mockData = fs.readFileSync(mockDataPath, 'utf8')
    return JSON.parse(mockData)
  } catch {
    logError('Could not load mock data, using minimal fallback')
    return [
      {
        id: 1,
        name: 'Sample Product',
        price: 29.99,
        stock_quantity: 10,
        category: 'Electronics',
        description: 'Sample product for testing',
        created_at: new Date().toISOString()
      }
    ]
  }
}


// Handle CORS preflight requests
export async function OPTIONS() {
  return handleCorsPreflightRequest()
}

// GET - Fetch all products with optional filtering and performance optimizations
export const GET = withErrorHandler(async (request: NextRequest) => {
  const startTime = Date.now()
  logApi('GET', '/api/products')

  const { searchParams } = new URL(request.url)

  // Optional filters
  const category = searchParams.get('category')
  const search = searchParams.get('search')
  const lowStock = searchParams.get('lowStock') === 'true'

  // Optional pagination - if not specified, return ALL products
  const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : null
  const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : null

  logApi('GET', '/api/products', undefined, undefined)

  try {
    let products, count

    // Try real database first with retry logic
    try {
      logApi('GET', '/api/products', undefined, undefined)
      const result = await withRetry(async () => {
        const { data, error: dbError } = await supabase
          .from('products')
          .select('*', { count: 'exact' })
          .order('created_at', { ascending: false })

        if (dbError) throw dbError
        return { data, count: data?.length || 0 }
      })

      products = result.data || []
      count = result.count
      const queryTime = Date.now() - startTime
      logDb('SELECT', 'products', queryTime, products.length)
    } catch (dbError) {
      const error = dbError as Error
      logError('Database error in products API', error, {
        component: 'ProductsAPI',
        operation: 'GET'
      })

      // Fallback to mock data only if database fails AND mock data is enabled
      const useMockData = process.env.USE_MOCK_DATA === 'true'
      if (useMockData) {
        logApi('GET', '/api/products/fallback')
        const mockProducts = getMockProducts()

        // Apply filters to mock data
        let filteredProducts = mockProducts

        if (category) {
          filteredProducts = filteredProducts.filter((p: Product) => p.category === category)
        }

        if (search) {
          filteredProducts = filteredProducts.filter((p: Product) =>
            p.name.toLowerCase().includes(search.toLowerCase())
          )
        }

        if (lowStock) {
          filteredProducts = filteredProducts.filter((p: Product) => p.stock_quantity < 10)
        }

        // Apply pagination to mock data
        if (page && limit) {
          const offset = (page - 1) * limit
          products = filteredProducts.slice(offset, offset + limit)
          logDb('SELECT', 'products (mock)', undefined, limit)
        } else {
          products = filteredProducts
          logDb('SELECT', 'products (mock)', undefined, filteredProducts.length)
        }

        count = filteredProducts.length
        logApi('GET', '/api/products/mock', 200, Date.now() - startTime)
      } else {
        // No fallback available, throw the database error
        const error = dbError as DatabaseError
        logError('Database query failed in products API', error, {
          component: 'ProductsAPI',
          operation: 'GET'
        })

        throw handleDatabaseError(error)
      }
    }

    const queryTime = Date.now() - startTime
    logDb('SELECT', 'products', queryTime, products?.length || 0)

    // Return response with or without pagination
    const responseData: ProductsApiResponse = {
      products: products || [],
    }

    if (page && limit) {
      responseData.pagination = {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    } else {
      responseData.total = count || 0
    }

    return successResponse(responseData)
  } catch (error) {
    const queryTime = Date.now() - startTime
    logError('Database query failed', error, { queryTime: `${queryTime}ms` })

    if (error instanceof Error && error.message === 'Database query timeout') {
      throw new Error('Database query timed out. Please try again or contact support if the issue persists.')
    }

    throw error
  }
})

// POST - Create new product
export const POST = withErrorHandler(async (request: NextRequest) => {
  const productData = await validateRequestBody(request, (body) => {
    // Type assertion for body to ensure it's an object
    const bodyData = body as Record<string, unknown>

    // Validate required fields
    validateRequiredFields(bodyData, ['name', 'net_weight', 'price', 'category'])

    return {
      name: String(bodyData.name).trim(),
      image_url: bodyData.image_url ? String(bodyData.image_url).trim() : null,
      image_public_id: bodyData.image_public_id ? String(bodyData.image_public_id).trim() : null,
      net_weight: String(bodyData.net_weight).trim(),
      price: parseFloat(bodyData.price as string),
      retail_price: bodyData.retail_price ? parseFloat(bodyData.retail_price as string) : null,
      stock_quantity: parseInt(bodyData.stock_quantity as string) || 0,
      category: String(bodyData.category).trim(),
    }
  })

  // Additional validation
  if (productData.price < 0) {
    return errorResponse('Unit price must be a positive number', 400)
  }

  if (productData.retail_price !== null && productData.retail_price < 0) {
    return errorResponse('Retail price must be a positive number', 400)
  }

  if (productData.stock_quantity < 0) {
    return errorResponse('Stock quantity must be a positive number', 400)
  }

  const { data: product, error } = await supabase
    .from('products')
    .insert([productData])
    .select()
    .single()

  if (error) {
    handleDatabaseError(error)
  }

  return successResponse(product, 'Product created successfully', 201)
})

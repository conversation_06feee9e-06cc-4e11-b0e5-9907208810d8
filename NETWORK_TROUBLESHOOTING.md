# Network Connectivity Issues - Immediate Solutions

## 🚨 Current Issue
Your system is experiencing `TypeError: fetch failed` errors when trying to connect to external services (Supabase, external APIs). This is a **network connectivity issue**, not a code problem.

## 🔧 Immediate Solutions (Try in Order)

### 1. **Windows Firewall/Antivirus Check**
```powershell
# Run as Administrator in PowerShell
# Temporarily disable Windows Firewall for testing
netsh advfirewall set allprofiles state off

# Test the application, then re-enable:
netsh advfirewall set allprofiles state on
```

**If you have antivirus software:**
- Temporarily disable real-time protection
- Add Node.js and your project folder to antivirus exclusions
- Add `node.exe` to firewall exceptions

### 2. **Network Configuration Reset**
```powershell
# Run as Administrator in PowerShell
# Reset network stack
netsh winsock reset
netsh int ip reset
ipconfig /flushdns
ipconfig /release
ipconfig /renew

# Restart your computer after running these commands
```

### 3. **Proxy/Corporate Network**
If you're on a corporate network:

```bash
# Check if proxy is set
echo $HTTP_PROXY
echo $HTTPS_PROXY

# If proxy is set, configure Node.js
npm config set proxy http://your-proxy:port
npm config set https-proxy http://your-proxy:port

# Or unset if not needed
npm config delete proxy
npm config delete https-proxy
```

### 4. **DNS Configuration**
Change your DNS servers to public ones:
- Primary: `*******` (Google)
- Secondary: `*******` (Google)
- Alternative: `*******` and `*******` (Cloudflare)

**Windows DNS Change:**
1. Open Network and Sharing Center
2. Click "Change adapter settings"
3. Right-click your network connection → Properties
4. Select "Internet Protocol Version 4 (TCP/IPv4)" → Properties
5. Select "Use the following DNS server addresses"
6. Enter the DNS servers above

### 5. **Node.js Network Configuration**
Add to your `.env.local`:
```bash
# Network configuration
NODE_TLS_REJECT_UNAUTHORIZED=0
UV_THREADPOOL_SIZE=128
```

### 6. **Alternative: Use Different Network**
- Try mobile hotspot
- Connect to different WiFi network
- Use VPN (if corporate network is blocking)

## 🧪 Testing Steps

After each solution, test with:
```bash
# Test 1: Basic connectivity
ping google.com

# Test 2: HTTPS connectivity
curl -I https://httpbin.org/get

# Test 3: Supabase connectivity
curl -I https://iidvfmruzoyrfboptggm.supabase.co/rest/v1/

# Test 4: Your application
npm run dev
# Then visit: http://localhost:3000/api/test-connection
```

## 🔍 Diagnostic Commands

Run these to gather more information:
```powershell
# Check network configuration
ipconfig /all

# Check DNS resolution
nslookup supabase.co
nslookup google.com

# Check firewall status
netsh advfirewall show allprofiles

# Check proxy settings
netsh winhttp show proxy

# Check network connectivity
telnet google.com 80
telnet supabase.co 443
```

## 🚀 Quick Workaround (Temporary)

If you need to continue development immediately, you can use a local mock:

1. **Create Mock Data File** (`src/data/mockProducts.json`):
```json
[
  {
    "id": 1,
    "name": "Sample Product 1",
    "price": 29.99,
    "stock_quantity": 100,
    "category": "Electronics",
    "description": "Sample product for testing",
    "created_at": "2024-01-01T00:00:00Z"
  }
]
```

2. **Use Mock in Development** (temporarily modify API route):
```javascript
// In src/app/api/products/route.ts - add at the top
if (process.env.NODE_ENV === 'development' && process.env.USE_MOCK_DATA === 'true') {
  const mockProducts = require('../../../data/mockProducts.json')
  return successResponse({ products: mockProducts, total: mockProducts.length })
}
```

3. **Add to .env.local**:
```bash
USE_MOCK_DATA=true
```

## 📞 If Nothing Works

This appears to be a system-level network configuration issue. Consider:

1. **Contact your IT department** (if corporate network)
2. **Check with your ISP** about connection issues
3. **Try from a different computer** to confirm it's system-specific
4. **Use Windows Network Troubleshooter**:
   - Settings → Network & Internet → Status → Network troubleshooter

## 🎯 Most Likely Solutions

Based on the error pattern, try these first:
1. **Disable Windows Firewall temporarily** (Solution #1)
2. **Reset network stack** (Solution #2)  
3. **Change DNS servers** (Solution #4)

The issue is definitely network-related, not code-related, since the Supabase server is reachable but Node.js fetch is failing.

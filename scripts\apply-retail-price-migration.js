#!/usr/bin/env node

/**
 * Apply retail price migration to the database
 * This script adds the retail_price column to the products table
 */

const fs = require('fs')
const path = require('path')

console.log('🔄 Applying retail price migration...')

// Read the migration file
const migrationPath = path.join(__dirname, '../database/migrations/add_retail_price_to_products.sql')

if (!fs.existsSync(migrationPath)) {
  console.error('❌ Migration file not found:', migrationPath)
  process.exit(1)
}

const migrationSQL = fs.readFileSync(migrationPath, 'utf8')

console.log('📋 Migration SQL loaded successfully')
console.log('📝 To apply this migration:')
console.log('1. Copy the SQL content from:', migrationPath)
console.log('2. Go to your Supabase dashboard > SQL Editor')
console.log('3. Paste the SQL and click "Run"')
console.log('')
console.log('🔧 Migration SQL Preview:')
console.log('=' .repeat(50))
console.log(migrationSQL.substring(0, 500) + '...')
console.log('=' .repeat(50))
console.log('')
console.log('✅ Migration ready to apply!')
console.log('📊 This will add retail_price column to products table')
console.log('💡 Existing products will get retail_price = unit_price * 1.2 (20% markup)')

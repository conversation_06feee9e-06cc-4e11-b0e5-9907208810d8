import { NextRequest, NextResponse } from 'next/server'

import {
  successResponse,
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  handleDatabaseError,
  handleCorsPreflightRequest
} from '@/lib/api-utils'
import { supabase } from '@/lib/supabase'
import type { CustomersApiResponse } from '@/types'

// Handle CORS preflight requests
export async function OPTIONS() {
  return handleCorsPreflightRequest()
}

// GET - Fetch all customers with optional pagination (NO DEFAULT LIMIT)
export const GET = withErrorHandler(async (request: NextRequest) => {
  const { searchParams } = new URL(request.url)

  // Optional filters
  const search = searchParams.get('search')

  // Optional pagination - if not specified, return ALL customers
  const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : null
  const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : null

  let query = supabase
    .from('customers')
    .select('*', { count: 'exact' })
    .order('created_at', { ascending: false })

  // Apply pagination only if specified
  if (page && limit) {
    const offset = (page - 1) * limit
    query = query.range(offset, offset + limit - 1)
  }

  // Apply search filter with exact name matching support
  if (search) {
    // Check if search contains both first and last name (space separated)
    const searchParts = search.trim().split(' ')

    if (searchParts.length >= 2) {
      // Try exact name matching first
      const firstName = searchParts[0]
      const lastName = searchParts.slice(1).join(' ')

      query = query.or(`and(customer_name.eq.${firstName},customer_family_name.eq.${lastName}),customer_name.ilike.%${search}%,customer_family_name.ilike.%${search}%,phone_number.ilike.%${search}%`)
    } else {
      // Single word search - use fuzzy matching
      query = query.or(`customer_name.ilike.%${search}%,customer_family_name.ilike.%${search}%,phone_number.ilike.%${search}%`)
    }
  }

  const { data: customers, error, count } = await query

  if (error) {
    return handleDatabaseError(error)
  }

  // Return response with or without pagination
  const responseData: CustomersApiResponse = {
    customers: customers || [],
  }

  if (page && limit) {
    responseData.pagination = {
      page,
      limit,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / limit)
    }
  } else {
    responseData.total = count || 0
  }

  return successResponse(responseData)
})

// POST - Create new customer or update existing
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      customer_name,
      customer_family_name,
      profile_picture_url,
      profile_picture_public_id,
      phone_number,
      address,
      birth_date,
      birth_place,
      notes,
    } = body

    // Validate required fields
    if (!customer_name || !customer_family_name) {
      return NextResponse.json(
        { error: 'Customer name and family name are required' },
        { status: 400 }
      )
    }

    // Check if customer already exists
    const { data: existingCustomer } = await supabase
      .from('customers')
      .select('id')
      .eq('customer_name', customer_name)
      .eq('customer_family_name', customer_family_name)
      .single()

    if (existingCustomer) {
      // Update existing customer - build update object dynamically with consistent logic
      const updateData: Record<string, string | null> = {
        profile_picture_url: profile_picture_url || null,
        profile_picture_public_id: profile_picture_public_id || null,
        notes: notes || null,
      }

      // Add all optional fields with consistent conditional logic
      if (phone_number !== undefined) {
        updateData.phone_number = phone_number || null
      }
      if (address !== undefined) {
        updateData.address = address || null
      }
      if (birth_date !== undefined) {
        updateData.birth_date = birth_date || null
      }
      if (birth_place !== undefined) {
        updateData.birth_place = birth_place || null
      }

      const { data: customer, error } = await supabase
        .from('customers')
        .update(updateData)
        .eq('id', existingCustomer.id)
        .select()
        .single()

      if (error) {
        console.error('Supabase error updating customer:', error)
        return NextResponse.json({ error: error.message }, { status: 500 })
      }

      return NextResponse.json({ customer }, { status: 200 })
    } else {
      // Create new customer - build insert object dynamically to handle missing columns
      const insertData: Record<string, string | null> = {
        customer_name,
        customer_family_name,
        profile_picture_url: profile_picture_url || null,
        profile_picture_public_id: profile_picture_public_id || null,
        phone_number: phone_number || null,
        address: address || null,
        notes: notes || null,
      }

      // Only add new columns if they exist in the request (for backward compatibility)
      if (birth_date !== undefined) {
        insertData.birth_date = birth_date || null
      }
      if (birth_place !== undefined) {
        insertData.birth_place = birth_place || null
      }

      const { data: customer, error } = await supabase
        .from('customers')
        .insert([insertData])
        .select()
        .single()

      if (error) {
        console.error('Supabase error creating customer:', error)
        return NextResponse.json({ error: error.message }, { status: 500 })
      }

      return NextResponse.json({ customer }, { status: 201 })
    }
  } catch (error) {
    console.error('Error managing customer:', error)
    return NextResponse.json(
      {
        error: 'Failed to manage customer record',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

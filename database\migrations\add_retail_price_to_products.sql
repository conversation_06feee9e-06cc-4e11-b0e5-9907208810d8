-- =====================================================
-- ADD RETAIL PRICE TO PRODUCTS TABLE
-- =====================================================
-- Migration to add retail_price column to products table
-- This allows tracking both unit price and retail price
--
-- 📋 VERSION: 1.0
-- 📅 CREATED: 2025-08-12
-- 🔧 COMPATIBILITY: Supabase PostgreSQL 15+
-- 🎯 PURPOSE: Add retail_price field for better pricing management
-- =====================================================

-- Add retail_price column to products table
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS retail_price DECIMAL(10,2);

-- Add constraint to ensure retail_price is non-negative when provided
ALTER TABLE products 
ADD CONSTRAINT IF NOT EXISTS products_retail_price_check 
CHECK (retail_price IS NULL OR retail_price >= 0);

-- Add comment to the column for documentation
COMMENT ON COLUMN products.retail_price IS 'Retail selling price (optional) - different from unit/wholesale price';

-- Update existing products to have retail_price = price * 1.2 (20% markup) as default
-- This is optional and can be customized based on business needs
UPDATE products 
SET retail_price = ROUND(price * 1.2, 2) 
WHERE retail_price IS NULL;

-- Add index for retail_price for better query performance
CREATE INDEX IF NOT EXISTS idx_products_retail_price ON products(retail_price);

-- Add composite index for price comparisons
CREATE INDEX IF NOT EXISTS idx_products_price_retail_price ON products(price, retail_price);

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify the column was added successfully
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'products' 
AND column_name IN ('price', 'retail_price')
ORDER BY ordinal_position;

-- Check sample data
SELECT 
    name,
    price as unit_price,
    retail_price,
    ROUND((retail_price - price) / price * 100, 1) as markup_percentage
FROM products 
LIMIT 5;

-- =====================================================
-- ROLLBACK INSTRUCTIONS (IF NEEDED)
-- =====================================================
-- To rollback this migration, run:
-- ALTER TABLE products DROP COLUMN IF EXISTS retail_price;
-- DROP INDEX IF EXISTS idx_products_retail_price;
-- DROP INDEX IF EXISTS idx_products_price_retail_price;
-- =====================================================

SELECT '✅ Retail price column added successfully to products table!' as status;

// =====================================================
// SETTINGS TYPE DEFINITIONS
// =====================================================
// Professional TypeScript types for settings management
// Ensures type safety across the settings system

// Database table types (matching the SQL schema)
export interface StoreSettingsDB {
  id: string
  store_name: string
  store_description: string
  store_address: string | null
  store_phone: string | null
  store_email: string | null
  store_logo_url: string | null
  store_logo_public_id: string | null
  currency: string
  timezone: string
  language: string
  business_hours: BusinessHours
  tax_settings: TaxSettings
  receipt_settings: ReceiptSettings
  created_at: string
  updated_at: string
}

export interface UserPreferencesDB {
  id: string
  user_id: string
  theme: 'light' | 'dark' | 'system'
  notifications: NotificationSettings
  dashboard_settings: DashboardSettings
  display_settings: DisplaySettings
  privacy_settings: PrivacySettings
  accessibility_settings: AccessibilitySettings
  created_at: string
  updated_at: string
}

export interface SystemSettingsDB {
  id: string
  setting_key: string
  setting_value: string | number | boolean | null
  setting_type: 'general' | 'security' | 'performance' | 'feature' | 'integration'
  description: string | null
  is_public: boolean
  created_at: string
  updated_at: string
}

// Component-friendly types (simplified for UI)
export interface StoreSettings {
  storeName: string
  storeDescription: string
  storeAddress: string
  storePhone: string
  storeEmail: string
  currency: string
  timezone: string
  language: string
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system'
  notifications: NotificationSettings
  dashboard: DashboardSettings
}

// Detailed settings interfaces
export interface BusinessHours {
  monday: DaySchedule
  tuesday: DaySchedule
  wednesday: DaySchedule
  thursday: DaySchedule
  friday: DaySchedule
  saturday: DaySchedule
  sunday: DaySchedule
}

export interface DaySchedule {
  open: string // HH:mm format
  close: string // HH:mm format
  closed: boolean
}

export interface TaxSettings {
  enabled: boolean
  rate: number // 0.12 for 12%
  inclusive: boolean
}

export interface ReceiptSettings {
  header: string
  footer: string
  show_logo: boolean
}

export interface NotificationSettings {
  email: boolean
  push: boolean
  sms: boolean
  lowStock: boolean
  newDebt: boolean
  payments: boolean
  marketing?: boolean
}

export interface DashboardSettings {
  autoRefresh: boolean
  refreshInterval: number // milliseconds
  defaultView: string
  compactMode?: boolean
  showWelcome?: boolean
}

export interface DisplaySettings {
  language: string
  dateFormat: string
  timeFormat: '12h' | '24h'
  currency: string
  numberFormat: string
}

export interface PrivacySettings {
  shareAnalytics: boolean
  allowCookies: boolean
  dataRetention: number // days
}

export interface AccessibilitySettings {
  highContrast: boolean
  largeText: boolean
  reduceMotion: boolean
  screenReader: boolean
}

// API request/response types
export interface SettingsAPIRequest {
  type: 'store' | 'user' | 'system'
  data: StoreSettings | UserPreferences | SystemSettingsDB[]
  userId?: string
}

export interface SettingsAPIResponse {
  success: boolean
  settings?: {
    store?: StoreSettingsDB | null
    user?: UserPreferencesDB | null
    system?: SystemSettingsDB[]
  }
  data?: unknown
  error?: string
  details?: string
  queryTime?: string
}

// Settings section configuration
export interface SettingsSection {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string; style?: React.CSSProperties }>
  description: string
}

// Form validation types
export interface SettingsValidation {
  isValid: boolean
  errors: Record<string, string>
}

// Settings constants
export const SUPPORTED_CURRENCIES = [
  { code: 'PHP', name: 'Philippine Peso', symbol: '₱' },
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
  { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
  { code: 'SGD', name: 'Singapore Dollar', symbol: 'S$' }
] as const

export const SUPPORTED_TIMEZONES = [
  { value: 'Asia/Manila', label: 'Asia/Manila (GMT+8)', country: 'Philippines' },
  { value: 'Asia/Singapore', label: 'Asia/Singapore (GMT+8)', country: 'Singapore' },
  { value: 'Asia/Tokyo', label: 'Asia/Tokyo (GMT+9)', country: 'Japan' },
  { value: 'UTC', label: 'UTC (GMT+0)', country: 'Universal' }
] as const

export const SUPPORTED_LANGUAGES = [
  { code: 'en', name: 'English', nativeName: 'English' },
  { code: 'fil', name: 'Filipino', nativeName: 'Filipino' },
  { code: 'tl', name: 'Tagalog', nativeName: 'Tagalog' },
  { code: 'es', name: 'Spanish', nativeName: 'Español' },
  { code: 'zh', name: 'Chinese', nativeName: '中文' }
] as const

export const REFRESH_INTERVALS = [
  { value: 60000, label: '1 minute' },
  { value: 300000, label: '5 minutes' },
  { value: 600000, label: '10 minutes' },
  { value: 1800000, label: '30 minutes' },
  { value: 3600000, label: '1 hour' }
] as const

// Default settings values
export const DEFAULT_STORE_SETTINGS: StoreSettings = {
  storeName: 'Revantad Store',
  storeDescription: 'Professional Sari-Sari Store Management System',
  storeAddress: '',
  storePhone: '',
  storeEmail: '',
  currency: 'PHP',
  timezone: 'Asia/Manila',
  language: 'en'
}

export const DEFAULT_USER_PREFERENCES: UserPreferences = {
  theme: 'light',
  notifications: {
    email: true,
    push: true,
    sms: false,
    lowStock: true,
    newDebt: true,
    payments: true
  },
  dashboard: {
    autoRefresh: true,
    refreshInterval: 300000,
    defaultView: 'dashboard'
  }
}

#!/usr/bin/env node

/**
 * Upload Issue Diagnostic and Fix Script
 * 
 * This script helps diagnose and fix common image upload issues
 * in the Revantad Store application.
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Revantad Store - Upload Issue Diagnostic\n');

// Check if .env.local exists
const envPath = path.join(__dirname, '..', '.env.local');
const envExamplePath = path.join(__dirname, '..', '.env.example');

console.log('📁 Checking environment files...');

if (!fs.existsSync(envPath)) {
  console.log('❌ .env.local file not found!');
  
  if (fs.existsSync(envExamplePath)) {
    console.log('📋 Found .env.example file. Creating .env.local...');
    
    try {
      const exampleContent = fs.readFileSync(envExamplePath, 'utf8');
      fs.writeFileSync(envPath, exampleContent);
      console.log('✅ Created .env.local from .env.example');
    } catch (error) {
      console.log('❌ Failed to create .env.local:', error.message);
    }
  } else {
    console.log('❌ .env.example file also not found!');
  }
} else {
  console.log('✅ .env.local file exists');
}

// Read and check environment variables
if (fs.existsSync(envPath)) {
  console.log('\n🔍 Checking Cloudinary configuration...');
  
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');
  
  const requiredVars = [
    'NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME',
    'CLOUDINARY_API_KEY',
    'CLOUDINARY_API_SECRET'
  ];
  
  const missingVars = [];
  const placeholderVars = [];
  
  requiredVars.forEach(varName => {
    const line = envLines.find(line => line.startsWith(`${varName}=`));
    
    if (!line) {
      missingVars.push(varName);
    } else {
      const value = line.split('=')[1]?.trim();
      if (!value || value.startsWith('your_') || value === 'placeholder') {
        placeholderVars.push(varName);
      } else {
        console.log(`✅ ${varName}: configured`);
      }
    }
  });
  
  if (missingVars.length > 0) {
    console.log('\n❌ Missing environment variables:');
    missingVars.forEach(varName => {
      console.log(`   - ${varName}`);
    });
  }
  
  if (placeholderVars.length > 0) {
    console.log('\n⚠️  Environment variables with placeholder values:');
    placeholderVars.forEach(varName => {
      console.log(`   - ${varName}`);
    });
  }
  
  if (missingVars.length > 0 || placeholderVars.length > 0) {
    console.log('\n📝 To fix this issue:');
    console.log('1. Go to https://cloudinary.com/console');
    console.log('2. Sign up or log in to your account');
    console.log('3. Copy your Cloud Name, API Key, and API Secret');
    console.log('4. Update your .env.local file with the real values');
    console.log('\nExample:');
    console.log('NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your-actual-cloud-name');
    console.log('CLOUDINARY_API_KEY=***************');
    console.log('CLOUDINARY_API_SECRET=your-actual-secret-key');
  } else {
    console.log('\n✅ All Cloudinary environment variables are configured!');
  }
}

// Check API route
const uploadRoutePath = path.join(__dirname, '..', 'src', 'app', 'api', 'upload', 'route.ts');

console.log('\n🔍 Checking upload API route...');

if (fs.existsSync(uploadRoutePath)) {
  console.log('✅ Upload API route exists');
  
  const routeContent = fs.readFileSync(uploadRoutePath, 'utf8');
  
  // Check for common issues
  if (routeContent.includes('cloudinary.config')) {
    console.log('✅ Cloudinary configuration found in route');
  } else {
    console.log('⚠️  Cloudinary configuration might be missing in route');
  }
  
  if (routeContent.includes('console.log') || routeContent.includes('console.error')) {
    console.log('✅ Logging enabled for debugging');
  } else {
    console.log('⚠️  Consider adding logging for debugging');
  }
} else {
  console.log('❌ Upload API route not found!');
}

// Provide troubleshooting steps
console.log('\n🔧 Troubleshooting Steps:');
console.log('1. Ensure all Cloudinary environment variables are set correctly');
console.log('2. Restart your development server after updating .env.local');
console.log('3. Check browser console for detailed error messages');
console.log('4. Run: npm run test-cloudinary (if available)');
console.log('5. Check Cloudinary dashboard for any account issues');

console.log('\n📞 Need help?');
console.log('- Cloudinary Documentation: https://cloudinary.com/documentation');
console.log('- Cloudinary Support: https://support.cloudinary.com');

console.log('\n✨ Upload issue diagnostic completed!');

// Performance monitoring utility for tracking API response times and errors

import { logger } from '@/lib/logger'

interface PerformanceMetric {
  endpoint: string
  method: string
  startTime: number
  endTime?: number
  duration?: number
  success?: boolean
  error?: string
  retryCount?: number
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = []
  private maxMetrics = 100 // Keep only last 100 metrics

  startTracking(endpoint: string, method: string = 'GET'): string {
    const id = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const metric: PerformanceMetric = {
      endpoint,
      method,
      startTime: Date.now()
    }
    
    this.metrics.push(metric)
    
    // Keep only recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics)
    }
    
    return id
  }

  endTracking(endpoint: string, success: boolean, error?: string, retryCount?: number) {
    const metric = this.metrics.find(m => 
      m.endpoint === endpoint && 
      !m.endTime && 
      Date.now() - m.startTime < 60000 // Within last minute
    )
    
    if (metric) {
      metric.endTime = Date.now()
      metric.duration = metric.endTime - metric.startTime
      metric.success = success
      metric.error = error || ''
      metric.retryCount = retryCount || 0
    }
  }

  getMetrics(endpoint?: string): PerformanceMetric[] {
    if (endpoint) {
      return this.metrics.filter(m => m.endpoint === endpoint && m.endTime)
    }
    return this.metrics.filter(m => m.endTime)
  }

  getAverageResponseTime(endpoint?: string): number {
    const metrics = this.getMetrics(endpoint)
    if (metrics.length === 0) return 0
    
    const totalDuration = metrics.reduce((sum, m) => sum + (m.duration || 0), 0)
    return totalDuration / metrics.length
  }

  getSuccessRate(endpoint?: string): number {
    const metrics = this.getMetrics(endpoint)
    if (metrics.length === 0) return 0
    
    const successCount = metrics.filter(m => m.success).length
    return (successCount / metrics.length) * 100
  }

  getRecentErrors(endpoint?: string, minutes: number = 5): PerformanceMetric[] {
    const cutoff = Date.now() - (minutes * 60 * 1000)
    const metrics = this.getMetrics(endpoint)
    
    return metrics.filter(m => 
      !m.success && 
      m.endTime && 
      m.endTime > cutoff
    )
  }

  generateReport(): string {
    const allMetrics = this.getMetrics()
    const recentMetrics = allMetrics.filter(m => 
      m.endTime && m.endTime > Date.now() - (5 * 60 * 1000) // Last 5 minutes
    )

    const report = `
Performance Report (Last 5 minutes):
=====================================
Total Requests: ${recentMetrics.length}
Average Response Time: ${Math.round(this.getAverageResponseTime())}ms
Success Rate: ${Math.round(this.getSuccessRate())}%

Recent Errors:
${this.getRecentErrors().map(m => 
  `- ${m.endpoint}: ${m.error} (${m.duration}ms)`
).join('\n') || 'None'}

Slowest Requests:
${recentMetrics
  .sort((a, b) => (b.duration || 0) - (a.duration || 0))
  .slice(0, 5)
  .map(m => `- ${m.endpoint}: ${m.duration}ms`)
  .join('\n') || 'None'}
    `.trim()

    return report
  }

  clear() {
    this.metrics = []
  }
}

// Global instance
export const performanceMonitor = new PerformanceMonitor()

// Helper function to wrap fetch with performance tracking
export async function trackedFetch(
  endpoint: string, 
  options?: RequestInit,
  retryCount: number = 0
): Promise<Response> {
  performanceMonitor.startTracking(endpoint, options?.method || 'GET')
  
  try {
    const response = await fetch(endpoint, options)
    performanceMonitor.endTracking(endpoint, response.ok, response.ok ? undefined : `HTTP ${response.status}`, retryCount)
    return response
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    performanceMonitor.endTracking(endpoint, false, errorMessage, retryCount)
    throw error
  }
}

// Console logging helper
export function logPerformanceReport() {
  logger.info('Performance Report', { report: performanceMonitor.generateReport() })
}

// Auto-log performance report every 5 minutes in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  setInterval(() => {
    const metrics = performanceMonitor.getMetrics()
    if (metrics.length > 0) {
      logger.info('Performance Report scheduled')
      logPerformanceReport()
    }
  }, 5 * 60 * 1000) // Every 5 minutes
}

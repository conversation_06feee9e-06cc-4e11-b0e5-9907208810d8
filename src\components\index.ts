// Component exports for better organization and cleaner imports
// This file serves as a central export point for all components

// Layout Components
export { default as AdminHeader } from './AdminHeader'
export { default as Sidebar } from './Sidebar'

// Dashboard Components
export { default as DashboardStats } from './DashboardStats'
export { default as APIGraphing } from './APIGraphing'

// Product Management Components
export { default as ProductsSection } from './ProductsSection'
export { default as ProductModal } from './ProductModal'
export { default as ProductQuickActions } from './ProductQuickActions'
export { default as ProductImageZoom } from './ProductImageZoom'

// Customer Management Components
export { default as CustomerAvatar } from './CustomerAvatar'
export { default as CustomerProfileModal } from './CustomerProfileModal'

// Debt Management Components
export { default as DebtSection } from './DebtSection'
export { default as DebtModal } from './DebtModal'
export { default as PaymentModal } from './PaymentModal'
export { default as CustomerDebtDetailsModal } from './CustomerDebtDetailsModal'



// UI Components
export { default as Toast, ToastContainer, useToast } from './Toast'

// Feature Components
export { default as FamilyGallery } from './FamilyGallery'
export { default as Calendar } from './Calendar'
export { default as History } from './History'
export { default as AIAssistant } from './AIAssistant'
export { default as AISupport } from './AISupport'
export { default as Settings } from './Settings'

// Utility Components
export { default as LoadingSpinner } from './LoadingSpinner'
export { default as LoadingSkeleton } from './LoadingSkeleton'
export { default as ProtectedRoute } from './ProtectedRoute'
export { ThemeProvider } from './ThemeProvider'

// Re-export commonly used types
export type { Product, CustomerDebt, CustomerPayment, CustomerBalance, PaymentMethod } from '@/lib/supabase'
